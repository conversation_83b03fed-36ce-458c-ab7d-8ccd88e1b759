import { Router } from "express";
import express from "express";
import speakeasy from "speakeasy";
import QRCode from "qrcode";
import crudDA from "../da/crudDA";
import dotenv from 'dotenv';
import { transporter } from "../da/integrationDA";
import { randomGID } from "../Ultis/convert";
dotenv.config();
const router = Router();

/**
 * @swagger
 * /2fa/setup:
 *   post:
 *     summary: Thiết lập 2FA
 *     description: Thiết lập 2FA cho người dùng
 *     tags: [2FA]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               Id:
 *                 type: string
     *             required:
 *               - Id
 *     responses:
 *       200:
 *         description: Thiết lập thành công
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: "Success"
 *                 data:
 *                   type: object
 *                   properties:
 *                     qrCode:
 *                       type: string
 *                       description: Đường dẫn QR code
 *                     secret:
 *                       type: string
 *                       description: Secret key cho 2FA
 *       404:
 *         description: <PERSON><PERSON><PERSON><PERSON> tìm thấy người dùng
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */

router.post('/setup', async (req, res) => {
    const { Id } = req.body;
    const customer = new crudDA(`data:${process.env.PID}:${"Customer"}`);
    const customerData = (await customer.getById(Id)) as any;
    if (!customerData) return res.status(404).json({ code: 404, message: "User not found" });
    const secret = speakeasy.generateSecret({
        name: `ChainivoAuthen-(${customerData.Mobile})`
    });
    customerData.SecretOTP = secret.base32;
    customerData.Status2FA = 1; // đang thiết lập
    await customer.action("edit", [{ Id: customerData.Id, SecretOTP: secret.base32, Status2FA: 1, IsEnable2FA: false }]);
    // Sinh QR code từ otpauth_url
    const qrDataUrl = await QRCode.toDataURL(secret.otpauth_url ?? "");
    res.json({ code: 200, message: "Success", data: { qrCode: qrDataUrl, secret: secret.base32 } });
});
/**
@swagger
 * /2fa/disable:
 *   post:
 *     summary: Vô hiệu hóa 2FA
 *     description: Vô hiệu hóa 2FA cho người dùng
 *     tags: [2FA]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               Id:
 *                 type: string
 *             required:
 *               - Id
 *     responses:
 *       200:
 *         description: Vô hiệu hóa thành công
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: "Success"
 *       404:
 *         description: Không tìm thấy người dùng
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */

router.post('/disable', async (req, res) => {
    const { Id } = req.body;
    const customer = new crudDA(`data:${process.env.PID}:${"Customer"}`);
    const customerData = (await customer.getById(Id)) as any;
    if (!customerData) return res.status(404).json({ code: 404, message: "User not found" });
    await customer.action("edit", [{ Id: customerData.Id, SecretOTP: "", Status2FA: 0, IsEnable2FA: false, Date2FA: 0 }]);
    res.json({ code: 200, message: "Success" });
});
/**
@swagger 
 * /2fa/verify:
 *   post:
 *     summary: Xác thực 2FA
 *     description: Xác thực mã OTP cho người dùng
 *     tags: [2FA]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               Id:
 *                 type: string
 *               token:
 *                 type: string
 *             required:
 *               - Id
 *               - token
 *     responses:
 *       200:
 *         description: Xác thực thành công
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: "Success"
 *       400:
 *         description: Thiếu thông tin
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       401:
 *         description: Mã OTP không hợp lệ
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */

router.post('/verify', async (req, res) => {
    const { Id, token } = req.body;
    const customer = new crudDA(`data:${process.env.PID}:${"Customer"}`);
    const user = (await customer.getById(Id)) as any;
    if (!user || !user.SecretOTP) return res.status(400).json({ error: 'Invalid user or 2FA not setup' });
    const isValid = speakeasy.totp.verify({
        secret: user.SecretOTP,
        encoding: 'base32',
        token,
        window: 1 // Chấp nhận lệch 1 bước (~30s)
    });

    if (isValid) {
        await customer.action("edit", [{ Id: user.Id, IsEnable2FA: true, Status2FA: 2, Date2FA: new Date().getTime() }]);
        return res.json({ code: 200, message: "Success", data: user });
    } else {
        return res.status(401).json({ code: 401, message: "Invalid token" });
    }
});
/**
@swagger
 * /2fa/protected-action:
 *   post:
 *     summary: Thực hiện hành động bảo mật
 *     description: Thực hiện hành động yêu cầu xác thực 2FA
 *     tags: [2FA]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               Id:
 *                 type: string
 *               token:
 *                 type: string
 *             required:
 *               - Id
 *               - token
 *     responses:
 *       200:
 *         description: Hành động thành công
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: "Success"
     *       401:
 *         description: Mã OTP không hợp lệ
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 401
 *                 message:
 *                   type: string
 *                   example: "Mã OTP không hợp lệ"
 *       403:
 *         description: 2FA chưa được kích hoạt
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 401
 *                 message:
 *                   type: string
 *                   example: "2FA chưa được kích hoạt"
 */
router.post('/protected-action', async (req, res) => {
    const { Id, token } = req.body;
    const customer = new crudDA(`data:${process.env.PID}:${"Customer"}`);
    const user = (await customer.getById(Id)) as any;

    if (!user || !user.IsEnable2FA || !user.IsVerify) return res.status(403).json({ code: 403, message: "2FA not enabled" });

    const isValid = speakeasy.totp.verify({
        secret: user.SecretOTP,
        encoding: 'base32',
        token,
        window: 1
    });

    if (!isValid) {
        return res.status(401).json({ code: 401, message: "Invalid token" });
    }

    // Nếu xác thực thành công
    res.json({ code: 200, message: "Success" });
});
//send mail
/**
@swagger
 * /2fa/send-mail:
 *   post:
 *     summary: Gửi mail
 *     description: Gửi mail
 *     tags: [2FA]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               email:
 *                 type: string
 *             required:
 *               - email
 *     responses:
 *       200:
 *         description: Gửi mail thành công
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: "Success"
 *       404:
 *         description: Thiếu email
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Lỗi gửi mail
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.post('/send-mail', async (req, res) => {
    const { email } = req.body;
    if (!email) return res.send({ code: 404, success: false, message: "missing email" });
    const otp = Math.floor(100000 + Math.random() * 900000).toString();
    const html = `<p>Mã OTP của bạn là:: <b>${otp}</b></p>`;
    const mailOptions = {
        from: '<EMAIL>',
        to: email,
        email,
        subject: 'Xác thực OTP',
        html,
    };
    //lưu vào data base
    const otpRepo = new crudDA(`data:${process.env.PID}:${"LogOtp"}`);
    await otpRepo.action("add", [{ Email: email, Message: otp, DateCreated: new Date().getTime(), Id: randomGID(), ExpriseDate: new Date().getTime() + 1000 * 60 * 5 }]);
    await transporter.sendMail(mailOptions, function (error, info) {
        if (error) {
            console.log(error);
            return res.send({ code: 500, success: false, message: "Email sending failed", error });
        } else {
            return res.send({ code: 200, success: true, message: "Email sent successfully", info });
        }
    });
});
//verify otp
/**
@swagger
 * /2fa/verify-otp:
 *   post:
 *     summary: Xác thực OTP
 *     description: Xác thực mã OTP
 *     tags: [2FA]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               email:
 *                 type: string
 *               otp:
 *                 type: string
 *             required:
 *               - email
 *               - otp
 *     responses:
 *       200:
 *         description: Xác thực thành công
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: "Success"
 *       400:
 *         description: Thiếu thông tin
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       401:
 *         description: Mã OTP không hợp lệ
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       404:
 *         description: Mã OTP không tồn tại
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.post('/verify-otp', async (req, res) => {
    const { email, otp, customerId } = req.body;
    if (!email || !otp) return res.send({ code: 404, success: false, message: "missing email or otp" });
    const otpRepo = new crudDA(`data:${process.env.PID}:${"LogOtp"}`);
    const otpData = (await otpRepo.search(1, 1, `@Email:("${email}")`, { RETURN: ["Id", "Message", "DateCreated", "ExpriseDate"] })) as any;
    if (!otpData.count) return res.send({ code: 404, success: false, message: "OTP not found" });
    const otpDataDetail = otpData.data[0];
    if (otpDataDetail.Message !== otp) return res.send({ code: 401, success: false, message: "Invalid OTP" });
    if (otpDataDetail.ExpriseDate < new Date().getTime()) return res.send({ code: 401, success: false, message: "OTP expired" });
    await otpRepo.action("delete", [{ otpDataDetail }]);
    const customer = new crudDA(`data:${process.env.PID}:${"Customer"}`);
    const customerData = (await customer.getById(customerId)) as any;
    if (!customerData) return res.send({ code: 404, success: false, message: "User not found" });
    await customer.action("edit", [{ Id: customerData.Id, IsVerify: true, EmailVerify:email }]);
    return res.send({ code: 200, success: true, message: "OTP verified" });
});
export default router;