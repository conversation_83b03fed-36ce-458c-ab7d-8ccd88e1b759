import crudDA from "../da/crudDA";
import intergrationDA from "../da/integrationDA";
import { money, randomGID } from "../Ultis/convert";
interface Props {
    pid: string;
    module: string;
    data: Array<any>;
    ids: Array<string>;
    action: string;
    authorization: string;
}
interface FCMRequest {
    noti: {
        title: string;
        body: string;
        imageUrl?: string; // Không bắt buộc
    };
    data: any;
    deviceToken?: string; // Chỉ dùng cho `sendMessageToDevice`
    deviceTokens?: string[]; // Chỉ dùng cho `sendMessageToGroup`
}
enum NotiType {
    Order = 1,
    Transaction = 2,
    Call = 3,
    Message = 4,
    Ref = 5,
}
export async function sendNotification({ pid, module, data, action }: any) {

    const notificationRepo = new crudDA(`data:${pid}:Notification`);
    const customerRepo = new crudDA(`data:${pid}:Customer`);
    switch (module) {
        case "Customer":
            if (action === "add") {
                const integration = new intergrationDA(pid);
                const notificationData: Array<any> = [];
                for (const customer of data) {
                    if (customer.ParentId) {
                        const parentCustomer = await customerRepo.getById(customer.ParentId) as any;
                        const deviceTokens = parentCustomer.DeviceToken ? parentCustomer.DeviceToken.split(",") : [];
                        const request: FCMRequest = {
                            noti: {
                                title: "Có lượt giới thiệu mới",
                                body: `Bạn vừa giới thiệu thành công ${customer.Name}`,
                            },
                            data: {
                                id: customer.Id,
                                name: customer.Name,
                                type: NotiType.Ref,
                                url: "TreeAffiliateDetail",
                            },
                            deviceTokens,
                        };
                        try {
                            await integration.sendMessageToGroup(request);
                            notificationData.push({
                                Id: randomGID(),
                                CustomerId: customer.ParentId,
                                DateCreated: new Date().getTime(),
                                Name: "Có lượt giới thiệu mới",
                                Type: NotiType.Ref,
                                Data: JSON.stringify(customer),
                                Content: `Bạn vừa giới thiệu thành công ${customer.Name}`,
                                Status: 1, // chưa đọc,
                                LinkApp: 'TreeAffiliateDetail'
                            });
                        } catch (error) {
                            console.log("Error sending notification to customer:", customer.Id, error);
                        }
                    }
                }
                if (notificationData.length > 0) {
                    await notificationRepo.action("add", notificationData);
                }
            }
            break;
        case "Order":
            const _shopRepo = new crudDA(`data:${pid}:Shop`);
            if (action === "add") {
                const shopIds = data.map((e: any) => e.ShopId);
                const shopData = await _shopRepo.getBylistId(shopIds);

                // Tạo map để mapping ShopId -> Shop Owner CustomerId
                const shopToOwnerMap = new Map();
                shopData.forEach((shop: any) => {
                    shopToOwnerMap.set(shop.Id, shop.CustomerId);
                });

                // Lấy thông tin tất cả shop owners
                const shopOwnerIds = Array.from(new Set(shopData.map((shop: any) => shop.CustomerId)));
                const shopOwnerData = await customerRepo.getBylistId(shopOwnerIds);
                const shopOwnerMap = new Map();
                shopOwnerData.forEach((owner: any) => {
                    shopOwnerMap.set(owner.Id, owner);
                });

                const integration = new intergrationDA(pid);
                const notificationData: Array<any> = [];

                // Gửi notification riêng biệt cho từng order
                for (const order of data) {
                    const shopOwnerId = shopToOwnerMap.get(order.ShopId);
                    if (!shopOwnerId) continue;

                    const shopOwner = shopOwnerMap.get(shopOwnerId);
                    if (!shopOwner || !shopOwner.DeviceToken) continue;

                    const deviceTokens = Array.isArray(shopOwner.DeviceToken) ? shopOwner.DeviceToken : [shopOwner.DeviceToken];

                    const request: FCMRequest = {
                        noti: {
                            title: "Đơn hàng mới",
                            body: `${order.Name} vừa đặt mua đơn hàng #${order.Code || order.Id} từ shop của bạn. Vui lòng xác nhận`,
                        },
                        data: {
                            id: order.Id,
                            type: NotiType.Order,
                            url: "OrderDetailPage?orderId=" + order.Id,
                        },
                        deviceTokens,
                    };

                    try {
                        await integration.sendMessageToGroup(request);
                    } catch (error) {
                        console.log("Error sending notification to shop owner:", shopOwnerId, error);
                    }

                    // Thêm vào database notification cho shop owner
                    notificationData.push({
                        Id: randomGID(),
                        CustomerId: shopOwnerId,
                        DateCreated: new Date().getTime(),
                        Name: "Đơn hàng mới",
                        Type: NotiType.Order,
                        Data: JSON.stringify(order),
                        Content: `${order.Name} vừa đặt mua đơn hàng #${order.Code || order.Id} từ shop của bạn. Vui lòng xác nhận`,
                        Status: 1, // chưa đọc,
                        LinkApp: 'OrderDetailPage'
                    });
                }

                // Lưu tất cả notification vào database
                if (notificationData.length > 0) {
                    await notificationRepo.action("add", notificationData);
                }
            }
            if (action === "edit") {
                if (data[0].Status < 4) {
                    const customerData = await customerRepo.getById(data[0].CustomerId) as any;
                    const ShopData = await _shopRepo.getById(data[0].ShopId) as any;
                    const deviceTokens = customerData.DeviceToken;
                    const integration = new intergrationDA(pid);
                    const request: FCMRequest = {
                        noti: {
                            title: `Đơn hàng ${data[0].Code}`,
                            body: data[0].Status != 3 ? `Đơn hàng ${data[0].Code} của bạn đã được cửa hàng ${ShopData.Name} xác nhận và đang trong quá trình giao hàng \n` : `Đơn hàng ${data[0].Code} của bạn đã được giao thành công từ cửa hàng ${ShopData.Name}`,
                        },
                        data: {
                            id: data[0].Id,
                            type: NotiType.Order,
                            url: "OrderDetailPage?orderId=" + data[0].Id,
                        },
                        deviceTokens,
                    };
                    await integration.sendMessageToGroup(request);
                    notificationRepo.action("add", [{
                        Id: randomGID(),
                        CustomerId: data[0].CustomerId,
                        DateCreated: new Date().getTime(),
                        Name: "Cập nhật đơn hàng",
                        Type: NotiType.Order,
                        Data: data[0],
                        Content: data[0].Status != 3 ? `Đơn hàng ${data[0].Code} của bạn đã được cửa hàng ${ShopData.Name} xác nhận và đang trong quá trình giao hàng \n` : `Đơn hàng ${data[0].Code} của bạn đã được giao thành công từ cửa hàng ${ShopData.Name}`,
                        Status: 1, // chưa đọc,
                        LinkApp: 'OrderDetailPage'
                    }]);
                } else {
                    const shopData = await _shopRepo.getById(data[0].ShopId) as any;
                    var customerId = '';
                    if (data[0].IsCustomer == true) customerId = data[0].CustomerId;
                    else customerId = shopData.CustomerId;
                    const customerData = await customerRepo.getById(customerId) as any;
                    const deviceTokens = customerData.DeviceToken;
                    const integration = new intergrationDA(pid);
                    const request: FCMRequest = {
                        noti: {
                            title: `Đơn hàng ${data[0].Code}`,
                            body: data[0].IsCustomer ? ` ${customerData.Name} vừa yêu cầu hủy đơn hàng ${data[0].Code} ` : ` Cửa hàng ${shopData.Name} vừa từ chối đơn hàng ${data[0].Code} của bạn`,
                        },
                        data: {
                            id: data[0].Id,
                            type: NotiType.Order,
                            url: "OrderDetailPage?orderId=" + data[0].Id,
                        },
                        deviceTokens,
                    };
                    await integration.sendMessageToGroup(request);
                    notificationRepo.action("add", [{
                        Id: randomGID(),
                        CustomerId: data[0].CustomerId,
                        DateCreated: new Date().getTime(),
                        Name: "Đơn hàng hủy",
                        Type: NotiType.Order,
                        Data: data[0],
                        Content: data[0].IsCustomer ? ` ${customerData.Name} vừa yêu cầu hủy đơn hàng ${data[0].Code} ` : ` Cửa hàng ${shopData.Name} vừa từ chối đơn hàng ${data[0].Code} của bạn`,
                        Status: 1, // chưa đọc,
                        LinkApp: 'OrderDetailPage'
                    }]);
                }
            }
            break;
        case "HistoryReward":
            if (action === "add") {
                const customerData = await customerRepo.getBylistId(data.map((e: any) => e.CustomerId)) as any;
                const integration = new intergrationDA(pid);
                const notificationData: Array<any> = [];
                const _orderRepo = new crudDA(`data:${pid}:${"Order"}`);
                for (const reward of data) {
                    const customer = customerData.find((e: any) => e.Id === reward.CustomerId);
                    const customerReceiver = customerData.find((e: any) => e.Id === reward.CustomerRecive);
                    const order = await _orderRepo.getById(reward.OrderId) as any;
                    if (!customer || !customer.DeviceToken) continue;
                    const deviceTokens = Array.isArray(customer.DeviceToken) ? customer.DeviceToken : [customer.DeviceToken];
                    switch (reward.Type) {
                        case 1: // hoa hồng
                            const request: FCMRequest = {
                                noti: {
                                    title: `+ ${money(reward.Value)} CAN point`,
                                    body: `Bạn nhận được ${money(reward.Value)} CAN point từ giao dịch mua hàng ${order.Code} của ${order.Name}`,
                                },
                                data: {
                                    id: reward.Id,
                                    type: NotiType.Transaction,
                                    url: "TransactionHistory",
                                },
                                deviceTokens,
                            };
                            try {
                                await integration.sendMessageToGroup(request);
                                notificationData.push({
                                    Id: randomGID(),
                                    CustomerId: reward.CustomerId,
                                    DateCreated: new Date().getTime(),
                                    Name: `+ ${money(reward.Value)} CAN point`,
                                    Type: NotiType.Transaction,
                                    Data: JSON.stringify(reward),
                                    Content: `Bạn nhận được ${money(reward.Value)} CAN point từ giao dịch mua hàng ${order.Code} của ${order.Name}`,
                                    Status: 1, // chưa đọc,
                                    LinkApp: 'TransactionHistory'
                                });
                            } catch (error) {
                                console.log("Error sending notification to customer:", reward.CustomerId, error);
                            }
                            break;

                        case 2: //transfer
                            const requestTransfer: FCMRequest = {
                                noti: {
                                    title: `+ ${money(reward.Value)} CAN point`,
                                    body: `Bạn nhận được ${money(reward.Value)} từ tài khoản của ${customerReceiver.Name}`,
                                },
                                data: {
                                    id: reward.Id,
                                    type: NotiType.Transaction,
                                    url: "TransactionHistory",
                                },
                                deviceTokens,
                            };
                            try {
                                await integration.sendMessageToGroup(requestTransfer);
                                notificationData.push({
                                    Id: randomGID(),
                                    CustomerId: reward.CustomerId,
                                    DateCreated: new Date().getTime(),
                                    Name: `+ ${money(reward.Value)} CAN point`,
                                    Type: NotiType.Transaction,
                                    Data: JSON.stringify(reward),
                                    Content: `Bạn nhận được ${money(reward.Value)} CAN point từ tài khoản của ${customerReceiver.Name}`,
                                    Status: 1, // chưa đọc,
                                    LinkApp: 'TransactionHistory'
                                });
                            } catch (error) {
                                console.log("Error sending notification to customer:", reward.CustomerId, error);
                            }
                            break;
                        case 5: // nhiệm vụ
                            if (reward.Status == 2) {
                                const requestMission: FCMRequest = {
                                    noti: {
                                        title: `+ ${money(reward.Value)} CAN point`,
                                        body: `Bạn nhận được ${money(reward.Value)} CAN point từ hoàn thành nhiệm vụ ${reward.Name}`,
                                    },
                                    data: {
                                        id: reward.Id,
                                        type: NotiType.Transaction,
                                        url: "TransactionHistory",
                                    },
                                    deviceTokens,
                                };
                                try {
                                    await integration.sendMessageToGroup(requestMission);
                                    notificationData.push({
                                        Id: randomGID(),
                                        CustomerId: reward.CustomerId,
                                        DateCreated: new Date().getTime(),
                                        Name: `+ ${money(reward.Value)} CAN point`,
                                        Type: NotiType.Transaction,
                                        Data: JSON.stringify(reward),
                                        Content: `Bạn nhận được ${money(reward.Value)} CAN point từ hoàn thành nhiệm vụ ${reward.Name}`,
                                        Status: 1, // chưa đọc,
                                        LinkApp: 'TransactionHistory'
                                    });
                                } catch (error) {
                                    console.log("Error sending notification to customer:", reward.CustomerId, error);
                                }
                            }
                            break;
                        default:
                            break;
                    }
                }
                if (notificationData.length > 0) {
                    await notificationRepo.action("add", notificationData);
                }
            }
            break;
        default:
            break;
    }

}
export async function sendnotiReward({ pid, data, code }: { pid: string; data: any, code: string }) {
    const customerRepo = new crudDA(`data:${pid}:Customer`);
    const integration = new intergrationDA(pid);
    const notificationRepo = new crudDA(`data:${pid}:Notification`);

    // Group data chỉ theo CustomerId (vì chỉ có 1 OrderId)
    const groupedData = data.reduce((acc: any, curr: any) => {
        const customerId = curr.CustomerId;
        if (!acc[customerId]) {
            acc[customerId] = {
                OrderId: curr.OrderId, // Lấy OrderId từ item đầu tiên
                CustomerId: customerId,
                rewards: [],
                totalValue: 0
            };
        }
        acc[customerId].rewards.push(curr);
        acc[customerId].totalValue += curr.Value;
        return acc;
    }, {});

    // Lấy thông tin Order và Customer một lần
    const orderId = data[0]?.OrderId; // Chỉ có 1 OrderId
    const customerIds = [...new Set(data.map((item: any) => item.CustomerId))] as string[];
    const [customerData] = await Promise.all([
        customerRepo.getBylistId(customerIds)
    ]);
    // Tạo map để lookup nhanh customer
    const customerMap = new Map();
    customerData.forEach((customer: any) => {
        customerMap.set(customer.Id, customer);
    });

    const notificationData: Array<any> = [];

    // Gửi notification cho từng customer
    for (const customerId in groupedData) {
        const group = groupedData[customerId];
        const customer = customerMap.get(customerId);
        const deviceTokens = customer?.DeviceToken ? customer.DeviceToken.split(",") : [];
        const request: FCMRequest = {
            noti: {
                title: `+ ${money(group.totalValue)} CAN point`,
                body: `Bạn nhận được ${money(group.totalValue)} CAN point từ đơn hàng ${code} vừa hoàn thành`,
            },
            data: {
                id: orderId, // Sử dụng OrderId để navigate đến chi tiết đơn hàng
                type: NotiType.Transaction,
                url: "TransactionHistory",
            },
            deviceTokens,
        };
        try {
            await integration.sendMessageToGroup(request);
            // Thêm notification vào database
            notificationData.push({
                Id: randomGID(),
                CustomerId: customerId,
                DateCreated: new Date().getTime(),
                Name: `+ ${money(group.totalValue)} CAN point`,
                Type: NotiType.Transaction,
                Data: JSON.stringify(group.rewards),
                Content: `Bạn nhận được ${money(group.totalValue)} CAN point từ đơn hàng ${code} vừa hoàn thành`,
                Status: 1, // chưa đọc,
                LinkApp: 'TransactionHistory'
            });
        } catch (error) {
            console.log("Error sending notification to customer:", customerId, error);
        }
    }
    // Lưu tất cả notification vào database
    if (notificationData.length > 0) {
        await notificationRepo.action("add", notificationData);
    }
}
export async function sendNotificationCall({ pid, data }: { pid: string; data: any }) {
    const customerRepo = new crudDA(`data:${pid}:Customer`);
    const integration = new intergrationDA(pid);
    const notificationRepo = new crudDA(`data:${pid}:Notification`);
    const customerData = await customerRepo.getById(data.targetUserId) as any;
    if (!customerData?.DeviceToken) return;
    const deviceTokens = customerData.DeviceToken.split(",");
    const request: FCMRequest = {
        noti: {
            title: `Cuộc gọi từ ${data.fromName}`,
            body: `Bạn có cuộc gọi từ ${data.fromName}`,
        },
        data: {
            type: NotiType.Call,
            callerId: data.targetUserId,
            callerName: data.fromName,
            callHistoryId: data.callHistoryId,
            url: "CallScreen",
        },
        deviceTokens,
    };
    try {
        await integration.sendMessageToGroup(request);
    } catch (error) {
        console.log("Error sending notification to customer:", data.targetUserId, error);
    }

}
export async function sendNotificationMessage({ pid, data }: { pid: string; data: any }) {
    const customerRepo = new crudDA(`data:${pid}:Customer`);
    const integration = new intergrationDA(pid);
    const customerData = await customerRepo.getById(data.customerId) as any;
    if (customerData?.DeviceToken) {
        const deviceTokens = customerData.DeviceToken.split(",");
        const request: FCMRequest = {
            noti: {
                title: data.fromName ? `Tin nhắn mới từ ${data.fromName}` : "Tin nhắn mới",
                body: data.fromName ? `Bạn có 1 tin nhắn mới từ ${data.fromName}` : `Bạn có 1 tin nhắn mới`,
            },
            data: {
                type: NotiType.Message,
                url: "ChatRoomScreen",
                id: data.roomId,
            },
            deviceTokens,
        };
        try {
            await integration.sendMessageToGroup(request);
        } catch (error) {
            console.log("Error sending notification to customer:", data.customerId, error);
        }
    }
    return;

}